/**
 * Ocean Soul Sparkles Admin - Error Management System
 * Centralized error handling and user feedback
 */

export interface ErrorDetails {
  code?: string;
  message: string;
  details?: any;
  timestamp: Date;
  context?: string;
  userMessage?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'booking' | 'payment' | 'pwa' | 'auth' | 'network' | 'validation' | 'system';
}

export interface ErrorHandlerOptions {
  showToast?: boolean;
  logToConsole?: boolean;
  reportToServer?: boolean;
  fallbackAction?: () => void;
}

class ErrorManager {
  private errorLog: ErrorDetails[] = [];
  private maxLogSize = 100;

  /**
   * Handle and process errors with appropriate user feedback
   */
  handleError(
    error: Error | string | any,
    context: string,
    category: ErrorDetails['category'],
    options: ErrorHandlerOptions = {}
  ): ErrorDetails {
    const {
      showToast = true,
      logToConsole = true,
      reportToServer = false,
      fallbackAction
    } = options;

    // Create error details
    const errorDetails: ErrorDetails = {
      code: error?.code || error?.status?.toString(),
      message: this.extractErrorMessage(error),
      details: error,
      timestamp: new Date(),
      context,
      severity: this.determineSeverity(error, category),
      category,
      userMessage: this.generateUserMessage(error, category)
    };

    // Log error
    if (logToConsole) {
      console.error(`[${category.toUpperCase()}] ${context}:`, errorDetails);
    }

    // Add to error log
    this.addToErrorLog(errorDetails);

    // Show user feedback
    if (showToast && typeof window !== 'undefined') {
      this.showUserFeedback(errorDetails);
    }

    // Report to server if needed
    if (reportToServer) {
      this.reportErrorToServer(errorDetails);
    }

    // Execute fallback action
    if (fallbackAction) {
      try {
        fallbackAction();
      } catch (fallbackError) {
        console.error('Fallback action failed:', fallbackError);
      }
    }

    return errorDetails;
  }

  /**
   * Handle booking-specific errors
   */
  handleBookingError(error: any, context: string = 'Booking Operation'): ErrorDetails {
    let userMessage = 'Failed to process booking. Please try again.';
    let severity: ErrorDetails['severity'] = 'medium';

    if (error?.status === 400) {
      if (error.error === 'Missing required fields') {
        userMessage = 'Please fill in all required fields and try again.';
        severity = 'low';
      } else if (error.error === 'Booking conflict') {
        userMessage = 'The selected time slot is no longer available. Please choose a different time.';
        severity = 'medium';
      } else if (error.error === 'Invalid reference') {
        userMessage = 'Invalid customer, service, or artist selected. Please refresh and try again.';
        severity = 'medium';
      }
    } else if (error?.status === 401) {
      userMessage = 'Your session has expired. Please log in again.';
      severity = 'high';
    } else if (error?.status >= 500) {
      userMessage = 'Server error occurred. Please try again in a few moments.';
      severity = 'high';
    }

    return this.handleError(error, context, 'booking', {
      showToast: true,
      logToConsole: true,
      reportToServer: severity === 'high' || severity === 'critical'
    });
  }

  /**
   * Handle PWA-specific errors
   */
  handlePWAError(error: any, context: string = 'PWA Operation'): ErrorDetails {
    let userMessage = 'App feature temporarily unavailable.';
    let severity: ErrorDetails['severity'] = 'low';

    if (context.includes('push notification')) {
      if (error.message?.includes('InvalidAccessError')) {
        userMessage = 'Push notifications are not properly configured. Some features may be limited.';
        severity = 'medium';
      } else if (error.message?.includes('NotAllowedError')) {
        userMessage = 'Push notifications are disabled. You can enable them in your browser settings.';
        severity = 'low';
      }
    } else if (context.includes('service worker')) {
      userMessage = 'Offline features may be limited. Please refresh the page.';
      severity = 'medium';
    }

    return this.handleError(error, context, 'pwa', {
      showToast: severity !== 'low',
      logToConsole: true,
      reportToServer: false
    });
  }

  /**
   * Handle payment-specific errors
   */
  handlePaymentError(error: any, context: string = 'Payment Processing'): ErrorDetails {
    let userMessage = 'Payment processing failed. Please try again.';
    let severity: ErrorDetails['severity'] = 'high';

    if (error?.status === 400) {
      userMessage = 'Invalid payment information. Please check your details and try again.';
      severity = 'medium';
    } else if (error?.code === 'CARD_DECLINED') {
      userMessage = 'Payment was declined. Please try a different payment method.';
      severity = 'medium';
    }

    return this.handleError(error, context, 'payment', {
      showToast: true,
      logToConsole: true,
      reportToServer: true
    });
  }

  /**
   * Extract meaningful error message from various error types
   */
  private extractErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }

    if (error?.message) {
      return error.message;
    }

    if (error?.error) {
      return typeof error.error === 'string' ? error.error : JSON.stringify(error.error);
    }

    if (error?.statusText) {
      return error.statusText;
    }

    return 'Unknown error occurred';
  }

  /**
   * Determine error severity based on error type and category
   */
  private determineSeverity(error: any, category: ErrorDetails['category']): ErrorDetails['severity'] {
    if (error?.status >= 500 || error?.code === 'NETWORK_ERROR') {
      return 'high';
    }

    if (error?.status === 401 || error?.status === 403) {
      return 'high';
    }

    if (category === 'payment') {
      return 'high';
    }

    if (category === 'booking' && error?.status === 400) {
      return 'medium';
    }

    if (category === 'pwa') {
      return 'low';
    }

    return 'medium';
  }

  /**
   * Generate user-friendly error message
   */
  private generateUserMessage(error: any, category: ErrorDetails['category']): string {
    const defaultMessages = {
      booking: 'Booking operation failed. Please try again.',
      payment: 'Payment processing failed. Please try again.',
      pwa: 'App feature temporarily unavailable.',
      auth: 'Authentication failed. Please log in again.',
      network: 'Network connection issue. Please check your internet connection.',
      validation: 'Please check your input and try again.',
      system: 'System error occurred. Please try again later.'
    };

    return defaultMessages[category] || 'An error occurred. Please try again.';
  }

  /**
   * Show user feedback (toast notification)
   */
  private showUserFeedback(errorDetails: ErrorDetails): void {
    // This would integrate with your toast notification system
    // For now, we'll use a simple console log
    console.log(`User Message: ${errorDetails.userMessage}`);
    
    // If you have react-toastify or similar, you would use it here:
    // toast.error(errorDetails.userMessage);
  }

  /**
   * Add error to internal log
   */
  private addToErrorLog(errorDetails: ErrorDetails): void {
    this.errorLog.unshift(errorDetails);
    
    // Keep log size manageable
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }
  }

  /**
   * Report error to server for monitoring
   */
  private async reportErrorToServer(errorDetails: ErrorDetails): Promise<void> {
    try {
      await fetch('/api/admin/errors/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...errorDetails,
          userAgent: navigator.userAgent,
          url: window.location.href
        })
      });
    } catch (reportError) {
      console.error('Failed to report error to server:', reportError);
    }
  }

  /**
   * Get recent errors for debugging
   */
  getRecentErrors(count: number = 10): ErrorDetails[] {
    return this.errorLog.slice(0, count);
  }

  /**
   * Clear error log
   */
  clearErrorLog(): void {
    this.errorLog = [];
  }

  /**
   * Get error statistics
   */
  getErrorStats(): { [category: string]: number } {
    const stats: { [category: string]: number } = {};
    
    this.errorLog.forEach(error => {
      stats[error.category] = (stats[error.category] || 0) + 1;
    });
    
    return stats;
  }
}

// Export singleton instance
export const errorManager = new ErrorManager();
export default errorManager;
